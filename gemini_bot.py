import os
from dotenv import load_dotenv, find_dotenv
import google.generativeai as genai

# Načítať premenné z .env súboru (aj ak nie je v aktuálnom adresári)
load_dotenv(find_dotenv())
API_KEY = os.getenv("API_KEY")

if not API_KEY:
    print("API kľúč nebol nájdený v .env súbore! Skontroluj cestu a názov premennej.")
    exit(1)

# Nastaviť API kľúč pre Gemini
try:
    genai.configure(api_key=API_KEY)
except Exception as e:
    print(f"Chyba pri nastavovaní API kľúča: {e}")
    exit(1)

# Vytvoriť model Gemini 2.5 Flash
try:
    model = genai.GenerativeModel('gemini-1.5-flash')
except Exception as e:
    print(f"Chyba pri vytváraní modelu: {e}")
    exit(1)

print("Spustený Gemini bot (2.5 Flash). Nap<PERSON><PERSON> 'koniec' pre ukončenie.")

while True:
    prompt = input("Ty: ")
    if prompt.lower() == 'koniec':
        print("Bot: Dovidenia!")
        break
    try:
        response = model.generate_content(prompt)
        print("Bot:", response.text)
    except Exception as e:
        print(f"Chyba pri získavaní odpovede: {e}")

import os
import json
import requests
from dotenv import load_dotenv, find_dotenv

# Načítať premenné z .env súboru (aj ak nie je v aktuálnom adresári)
load_dotenv(find_dotenv())
API_KEY = os.getenv("GEMINI_API_KEY")

if not API_KEY:
    print("API kľúč nebol nájdený v .env súbore! Skontroluj cestu a názov premennej.")
    exit(1)

# API endpoint pre Gemini
API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key={API_KEY}"

print("Spustený Gemini bot (2.0 Flash). Napíš 'koniec' pre ukončenie.")

while True:
    prompt = input("Ty: ")
    if prompt.lower() == 'koniec':
        print("Bot: Dovidenia!")
        break
    try:
        # Priprav požiadavku
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ]
        }

        headers = {
            "Content-Type": "application/json"
        }

        # Pošli požiadavku
        response = requests.post(API_URL, json=payload, headers=headers)

        if response.status_code == 200:
            data = response.json()
            if 'candidates' in data and len(data['candidates']) > 0:
                text = data['candidates'][0]['content']['parts'][0]['text']
                print("Bot:", text)
            else:
                print("Bot: Nepodarilo sa získať odpoveď.")
        else:
            print(f"Bot: Chyba API: {response.status_code}")
            print(f"Odpoveď: {response.text}")

    except Exception as e:
        print(f"Chyba pri získavaní odpovede: {e}")

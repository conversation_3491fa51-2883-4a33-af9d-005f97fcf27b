import os
import json
import requests
from dotenv import load_dotenv, find_dotenv
from urllib.parse import urlparse
import sys
import time
import subprocess

try:
    from bs4 import BeautifulSoup
except ImportError:
    print("Chyba: Knižnica 'BeautifulSoup4' nie je nainštalova<PERSON>.")
    print("Pre prehliadanie webu ju prosím nainštalujte: pip install beautifulsoup4 requests")
    sys.exit(1)

try:
    import pyautogui
except ImportError:
    print("Chyba: Knižnica 'pyautogui' nie je nainštalova<PERSON>.")
    print("Pre automatizáciu PC ju prosím nainštalujte: pip install pyautogui Pillow")
    sys.exit(1)

# Načítať premenné z .env súboru (aj ak nie je v aktuálnom adresári)
load_dotenv(find_dotenv())
API_KEY = os.getenv("GEMINI_API_KEY")

if not API_KEY:
    print("API kľúč nebol nájdený v .env súbore! Skontroluj cestu a názov premennej.")
    exit(1)

# API endpoint pre Gemini
API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key={API_KEY}"

# Globálne hlavičky pre webové požiadavky
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

def fetch_web_content(url):
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status() # Vyvolá HTTPError pre zlé odpovede (4xx alebo 5xx)
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Odstránime skripty a štýly
        for script in soup(["script", "style"]):
            script.extract()
            
        # Získame text
        text = soup.get_text()
        
        # Rozdelíme na riadky a odstránime prázdne riadky a prebytočné medzery
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        return text
    except requests.exceptions.RequestException as e:
        return f"Chyba pri načítaní stránky: {e}"
    except Exception as e:
        return f"Neočekávaná chyba pri spracovaní stránky: {e}"

def automate_pc_task(task_description):
    print(f"Bot: Pokúšam sa automatizovať úlohu: {task_description}")
    try:
        if "otvor poznamkovy blok" in task_description.lower():
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write('notepad')
            time.sleep(0.5)
            pyautogui.press('enter')
            print("Bot: Otvoril som Poznámkový blok.")
            return "Poznámkový blok bol otvorený."
        elif "napis do poznamkoveho bloku" in task_description.lower():
            text_to_write = task_description.split("napis do poznamkoveho bloku", 1)[1].strip()
            pyautogui.write(text_to_write, interval=0.05)
            print(f"Bot: Napísal som '{text_to_write}' do Poznámkového bloku.")
            return f"Text '{text_to_write}' bol napísaný do Poznámkového bloku."
        elif "otvor prehliadac" in task_description.lower():
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write('chrome') # Alebo 'firefox', 'edge'
            time.sleep(0.5)
            pyautogui.press('enter')
            print("Bot: Otvoril som webový prehliadač.")
            return "Webový prehliadač bol otvorený."
        elif "otvor program" in task_description.lower():
            program_name = task_description.split("otvor program", 1)[1].strip()
            pyautogui.press('win')
            time.sleep(1)
            pyautogui.write(program_name)
            time.sleep(0.5)
            pyautogui.press('enter')
            print(f"Bot: Pokúsil som sa otvoriť program: {program_name}.")
            return f"Pokúsil som sa otvoriť program: {program_name}."
        else:
            return "Bot: Túto automatizačnú úlohu zatiaľ nepoznám."
    except Exception as e:
        return f"Bot: Chyba pri automatizácii úlohy: {e}"

def generate_and_save_code(programming_task):
    print(f"Bot: Generujem kód pre úlohu: {programming_task}")
    try:
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": f"Napíš Python kód pre nasledujúcu úlohu: {programming_task}. Kód by mal byť kompletný a spustiteľný. Nepridávaj žiadne vysvetlenia ani dodatočný text, len čistý kód."
                        }
                    ]
                }
            ]
        }

        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(API_URL, json=payload, headers=headers)

        if response.status_code == 200:
            data = response.json()
            if 'candidates' in data and len(data['candidates']) > 0:
                generated_code = data['candidates'][0]['content']['parts'][0]['text']
                
                file_name = "generated_code.py"
                with open(file_name, "w", encoding="utf-8") as f:
                    f.write(generated_code)
                
                print(f"Bot: Kód bol vygenerovaný a uložený do súboru '{file_name}'.")
                print(f"Môžete ho spustiť príkazom: python {file_name}")
                return f"Kód pre '{programming_task}' bol vygenerovaný a uložený do '{file_name}'. Spustite ho príkazom: python {file_name}"
            else:
                return "Bot: Nepodarilo sa vygenerovať kód."
        else:
            return f"Bot: Chyba API pri generovaní kódu: {response.status_code}\nOdpoveď: {response.text}"

    except Exception as e:
        return f"Bot: Chyba pri generovaní a ukladaní kódu: {e}"

def get_gemini_response(prompt_text):
    """Odošle prompt na Gemini API a vráti odpoveď."""
    try:
        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt_text
                        }
                    ]
                }
            ]
        }
        headers = {
            "Content-Type": "application/json"
        }
        response = requests.post(API_URL, json=payload, headers=headers)
        response.raise_for_status() # Vyvolá HTTPError pre zlé odpovede (4xx alebo 5xx)
        
        data = response.json()
        if 'candidates' in data and len(data['candidates']) > 0:
            return data['candidates'][0]['content']['parts'][0]['text']
        else:
            return "Nepodarilo sa získať odpoveď."
    except requests.exceptions.RequestException as e:
        return f"Chyba API: {e}"
    except Exception as e:
        return f"Chyba pri získavaní odpovede: {e}"

def process_command(prompt):
    """Spracuje príkaz od používateľa a vráti odpoveď bota."""
    if prompt.lower().startswith("prehliadaj "):
        url = prompt[len("prehliadaj "):].strip()
        if not urlparse(url).scheme:
            url = "https://" + url
        
        web_content = fetch_web_content(url)
        
        # Posielame obsah stránky na analýzu Gemini
        analysis_prompt = f"Používateľ ma požiadal, aby som prehliadol túto webovú stránku. Tu je jej obsah:\n\n{web_content}\n\nZhrň, prosím, hlavné body alebo odpovedz na otázku, ak bola položená v súvislosti s obsahom stránky."
        analysis_result = get_gemini_response(analysis_prompt)
        
        return f"Bot (obsah stránky):\n{web_content[:1000]}...\nBot (analýza stránky): {analysis_result}" if len(web_content) > 1000 else f"Bot (obsah stránky):\n{web_content}\nBot (analýza stránky): {analysis_result}"
    
    elif prompt.lower().startswith("automatizuj "):
        task = prompt[len("automatizuj "):].strip()
        return automate_pc_task(task)
    
    elif prompt.lower().startswith("programuj "):
        programming_task = prompt[len("programuj "):].strip()
        return generate_and_save_code(programming_task)
    
    else:
        return get_gemini_response(prompt)

if __name__ == "__main__":
    print("Spustený Gemini bot (2.5 Flash). Napíš 'koniec' pre ukončenie.")
    print("Pre prehliadanie webu použi príkaz: 'prehliadaj [URL]'")
    print("Pre automatizáciu PC použi príkaz: 'automatizuj [úloha]'")
    print("Pre programovanie použi príkaz: 'programuj [popis úlohy]'")

    while True:
        prompt = input("Ty: ")
        if prompt.lower() == 'koniec':
            print("Bot: Dovidenia!")
            break
        
        response_text = process_command(prompt)
        print("Bot:", response_text)

#!/usr/bin/env python3
"""
Jednoduchý chat s Gemini AI cez terminál
"""

import os
import sys

try:
    import google.generativeai as genai
    from dotenv import load_dotenv
except ImportError as e:
    print(f"Chyba: Potrebná knižnica nie je nainštalovaná: {e}")
    print("Spustite: pip install google-generativeai python-dotenv")
    sys.exit(1)

def main():
    # Načítaj .env súbor
    load_dotenv()

    # Skontrolujte, či máte API kľúč
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Chyba: GEMINI_API_KEY nie je nastavený v .env súbore.")
        print("Pridajte do .env súboru: GEMINI_API_KEY=váš_api_kľúč")
        print("API kľúč získajte z: https://makersuite.google.com/app/apikey")
        sys.exit(1)
    
    # Konfigurácia
    genai.configure(api_key=api_key)
    model = genai.GenerativeModel('gemini-pro')
    
    print("=== Gemini Chat ===")
    print("Napíšte 'exit' alebo 'quit' pre ukončenie")
    print("-" * 30)
    
    while True:
        try:
            user_input = input("\nVy: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'koniec']:
                print("Dovidenia!")
                break
                
            if not user_input:
                continue
                
            print("Gemini: ", end="", flush=True)
            response = model.generate_content(user_input)
            print(response.text)
            
        except KeyboardInterrupt:
            print("\n\nDovidenia!")
            break
        except Exception as e:
            print(f"Chyba: {e}")

if __name__ == "__main__":
    main()

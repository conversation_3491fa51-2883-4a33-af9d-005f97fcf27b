# Návod na inštaláciu Google Cloud SDK (gcloud CLI) pre Windows

1. Stiahni inštalátor z oficiálnej stránky:
   https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe

2. Spusti stiahnutý súbor GoogleCloudSDKInstaller.exe a postupuj podľa pokynov (ponechaj predvolené nastavenia).

3. Po úspešnej inštalácii otvor nový terminál (PowerShell alebo CMD) a zadaj:
   gcloud init

4. Prihlás sa do svojho Google účtu a nastav projekt podľa pokynov v termináli.

---

<PERSON><PERSON> ch<PERSON>š inštalovať SDK cez príkazový riadok (napríklad v skripte), m<PERSON><PERSON><PERSON><PERSON> použ<PERSON>:

```
Invoke-WebRequest -Uri "https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe" -OutFile "GoogleCloudSDKInstaller.exe"
Start-Process .\GoogleCloudSDKInstaller.exe
```

---

Po inštalácii môžeš používať príkazy ako:
- gcloud auth login
- gcloud projects list
- gcloud ai models list

Ak budeš potrebovať pomoc s konkrétnou službou Google Cloud, napíš mi!

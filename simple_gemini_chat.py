#!/usr/bin/env python3
"""
Jednoduchý chat s Gemini AI cez REST API (bez grpcio závislostí)
"""

import os
import sys
import json
import requests

try:
    from dotenv import load_dotenv
except ImportError:
    print("Chyba: python-dotenv nie je nainštalované.")
    print("Spustite: pip install python-dotenv requests")
    sys.exit(1)

def main():
    # Načítaj .env súbor
    load_dotenv()
    
    # Skontrolujte, či máte API kľúč
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Chyba: GEMINI_API_KEY nie je nastavený v .env súbore.")
        print("Pridajte do .env súboru: GEMINI_API_KEY=váš_api_kľúč")
        print("API kľúč získajte z: https://makersuite.google.com/app/apikey")
        return
    
    # API endpoint pre Gemini
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
    
    print("=== Gemini Chat (REST API) ===")
    print("Napíšte 'exit' alebo 'quit' pre ukončenie")
    print("-" * 40)
    
    while True:
        try:
            user_input = input("\nVy: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'koniec']:
                print("Dovidenia!")
                break
                
            if not user_input:
                continue
            
            # Priprav požiadavku
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": user_input
                            }
                        ]
                    }
                ]
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            print("Gemini: ", end="", flush=True)
            
            # Pošli požiadavku
            response = requests.post(url, json=payload, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if 'candidates' in data and len(data['candidates']) > 0:
                    text = data['candidates'][0]['content']['parts'][0]['text']
                    print(text)
                else:
                    print("Nepodarilo sa získať odpoveď.")
            else:
                print(f"Chyba API: {response.status_code}")
                print(f"Odpoveď: {response.text}")
                
        except KeyboardInterrupt:
            print("\n\nDovidenia!")
            break
        except Exception as e:
            print(f"Chyba: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Jednoduchý chat s Gemini AI cez REST API (bez grpcio závislostí)
"""

import os
import sys
import json
import requests
import subprocess

try:
    from dotenv import load_dotenv
except ImportError:
    print("Chyba: python-dotenv nie je nainštalované.")
    print("Spustite: pip install python-dotenv requests")
    sys.exit(1)

def main():
    # Načítaj .env súbor
    load_dotenv()
    
    # Skontrolujte, či máte API kľúč
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("Chyba: GEMINI_API_KEY nie je nastavený v .env súbore.")
        print("Pridajte do .env súboru: GEMINI_API_KEY=váš_api_kľúč")
        print("API kľúč získajte z: https://makersuite.google.com/app/apikey")
        return
    
    # API endpoint pre Gemini
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    print("=== Gemini Chat (REST API) ===")
    print("Napíšte 'exit' alebo 'quit' pre ukončenie")
    print("-" * 40)
    
    while True:
        try:
            user_input = input("\nVy: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'koniec']:
                print("Dovidenia!")
                break
                
            if not user_input:
                continue

            if user_input.lower().startswith("spusti "):
                program_name = user_input[len("spusti "):].strip()
                print(f"Spúšťam program: {program_name}")
                try:
                    result = subprocess.run(program_name, shell=True, capture_output=True, text=True, check=False)
                    if result.returncode == 0:
                        print(f"Program '{program_name}' bol spustený.")
                        if result.stdout:
                            print("Výstup programu:")
                            print(result.stdout)
                    else:
                        print(f"Program '{program_name}' sa nespustil úspešne. Návratový kód: {result.returncode}")
                        if result.stderr:
                            print("Chyba programu:")
                            print(result.stderr)
                except FileNotFoundError:
                    print(f"Chyba: Program '{program_name}' sa nenašiel. Skúste zadať celú cestu k programu.")
                except Exception as e:
                    print(f"Neočekávaná chyba pri spúšťaní programu: {e}")
                continue

            if user_input.lower().startswith("otvor web "):
                url_to_open = user_input[len("otvor web "):].strip()
                print(f"Otváram webovú stránku: {url_to_open}")
                try:
                    subprocess.Popen(f"start {url_to_open}", shell=True)
                    print(f"Webová stránka '{url_to_open}' bola otvorená v predvolenom prehliadači.")
                except Exception as e:
                    print(f"Chyba pri otváraní webovej stránky: {e}")
                continue

            if user_input.lower().startswith("hladaj "):
                search_query = user_input[len("hladaj "):].strip()
                search_url = f"https://www.google.com/search?q={search_query}"
                print(f"Vyhľadávam na internete: {search_query}")
                try:
                    subprocess.Popen(f"start {search_url}", shell=True)
                    print(f"Výsledky vyhľadávania pre '{search_query}' boli otvorené v predvolenom prehliadači.")
                except Exception as e:
                    print(f"Chyba pri vyhľadávaní na internete: {e}")
                continue
            
            # Priprav požiadavku
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": "Ak je otázka používateľa o vyhľadávaní informácií na internete, odpovedzte vo formáte '[SEARCH_QUERY]: [vyhľadávací dotaz]'. Inak odpovedajte normálne."
                            },
                            {
                                "text": user_input
                            }
                        ]
                    }
                ]
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            print("Gemini: ", end="", flush=True)
            
            # Pošli požiadavku
            response = requests.post(url, json=payload, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if 'candidates' in data and len(data['candidates']) > 0:
                    text = data['candidates'][0]['content']['parts'][0]['text']
                    print(text)
                else:
                    print("Nepodarilo sa získať odpoveď.")
            else:
                print(f"Chyba API: {response.status_code}")
                print(f"Odpoveď: {response.text}")
                
        except KeyboardInterrupt:
            print("\n\nDovidenia!")
            break
        except Exception as e:
            print(f"Chyba: {e}")

if __name__ == "__main__":
    main()

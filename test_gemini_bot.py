import unittest
import os
from unittest.mock import patch, MagicMock
from io import StringIO
import sys
import importlib
import requests
from bs4 import BeautifulSoup
import pyautogui
import time

# Predpokladáme, že gemini_bot.py je v rovnakom adresári
# Ak by bol v inom, bolo by potrebné upraviť import cesty
# Napr. sys.path.append('../cesta/k/botovi')

class TestGeminiBot(unittest.TestCase):

    def setUp(self):
        # Nastavíme testovací API kľúč pre .env
        self.test_api_key = "TEST_API_KEY_123"
        with open(".env", "w") as f:
            f.write(f"GEMINI_API_KEY={self.test_api_key}\n")
        
        # Uložíme pôvodné stdout, aby sme mohli zachytiť výstup print()
        self.held_stdout = sys.stdout
        self.captured_output = StringIO()
        sys.stdout = self.captured_output

    def tearDown(self):
        # Obnovíme pôvodné stdout
        sys.stdout = self.held_stdout
        # Vymažeme testovací .env súbor
        if os.path.exists(".env"):
            os.remove(".env")

    def test_api_key_loading(self):
        # Tento test overí, či sa API kľúč načíta správne z .env
        import gemini_bot
        importlib.reload(gemini_bot) # Znovu načíta modul, aby sa prečítal nový .env
        self.assertEqual(gemini_bot.API_KEY, self.test_api_key)

    @patch('gemini_bot.get_gemini_response')
    def test_process_command_general_query(self, mock_get_gemini_response):
        import gemini_bot
        mock_get_gemini_response.return_value = "Odpoveď na všeobecnú otázku."
        result = gemini_bot.process_command("Ahoj, ako sa máš?")
        self.assertEqual(result, "Odpoveď na všeobecnú otázku.")
        mock_get_gemini_response.assert_called_once_with("Ahoj, ako sa máš?")

    @patch('gemini_bot.fetch_web_content')
    @patch('gemini_bot.get_gemini_response')
    def test_process_command_browse_web(self, mock_get_gemini_response, mock_fetch_web_content):
        import gemini_bot
        mock_fetch_web_content.return_value = "Obsah testovacej stránky."
        mock_get_gemini_response.return_value = "Analýza testovacej stránky."
        result = gemini_bot.process_command("prehliadaj example.com")
        self.assertIn("Bot (obsah stránky):\nObsah testovacej stránky.\nBot (analýza stránky): Analýza testovacej stránky.", result)
        mock_fetch_web_content.assert_called_once_with("https://example.com")
        mock_get_gemini_response.assert_called_once() # Kontrola, či sa volá s obsahom stránky

    @patch('gemini_bot.automate_pc_task')
    def test_process_command_automate_pc(self, mock_automate_pc_task):
        import gemini_bot
        mock_automate_pc_task.return_value = "PC úloha vykonaná."
        result = gemini_bot.process_command("automatizuj otvor poznamkovy blok")
        self.assertEqual(result, "PC úloha vykonaná.")
        mock_automate_pc_task.assert_called_once_with("otvor poznamkovy blok")

    @patch('gemini_bot.generate_and_save_code')
    def test_process_command_program(self, mock_generate_and_save_code):
        import gemini_bot
        mock_generate_and_save_code.return_value = "Kód bol vygenerovaný."
        result = gemini_bot.process_command("programuj hru hadik")
        self.assertEqual(result, "Kód bol vygenerovaný.")
        mock_generate_and_save_code.assert_called_once_with("hru hadik")

    @patch('gemini_bot.requests.post')
    def test_get_gemini_response_success(self, mock_post):
        # Mockovanie úspešnej odpovede z Gemini API
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "candidates": [
                {
                    "content": {
                        "parts": [
                            {"text": "Ahoj, som testovacia odpoveď!"}
                        ]
                    }
                }
            ]
        }
        mock_post.return_value = mock_response

        import gemini_bot
        result = gemini_bot.get_gemini_response("Testovacia otázka")
        self.assertEqual(result, "Ahoj, som testovacia odpoveď!")
        mock_post.assert_called_once()

    @patch('gemini_bot.requests.post')
    def test_get_gemini_response_failure(self, mock_post):
        # Mockovanie neúspešnej odpovede z Gemini API
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError(
            "500 Client Error: Internal Server Error for url: http://test.com", response=mock_response
        )
        mock_post.return_value = mock_response

        import gemini_bot
        result = gemini_bot.get_gemini_response("Testovacia otázka")
        # Očakávame, že chybová správa bude obsahovať len časť z pôvodnej chyby
        self.assertIn("Chyba API: 500 Client Error: Internal Server Error for url:", result)
        mock_post.assert_called_once()

    @patch('gemini_bot.requests.get')
    @patch('gemini_bot.BeautifulSoup', return_value=MagicMock(get_text=lambda: "Mocked web content"))
    def test_fetch_web_content_success(self, mock_bs, mock_get):
        import gemini_bot
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = "<html><body><p>Test content</p></body></html>"
        mock_get.return_value = mock_response

        url = "http://test.com"
        content = gemini_bot.fetch_web_content(url)
        self.assertEqual(content, "Mocked web content")
        mock_get.assert_called_once_with(url, headers=gemini_bot.HEADERS, timeout=10) # Zmena na HEADERS

    @patch('gemini_bot.requests.get', side_effect=requests.exceptions.RequestException("Test error"))
    def test_fetch_web_content_failure(self, mock_get):
        import gemini_bot
        url = "http://test.com"
        content = gemini_bot.fetch_web_content(url)
        self.assertIn("Chyba pri načítaní stránky: Test error", content)

    @patch('gemini_bot.pyautogui.press')
    @patch('gemini_bot.pyautogui.write')
    def test_automate_pc_task_notepad(self, mock_write, mock_press):
        import gemini_bot
        result = gemini_bot.automate_pc_task("otvor poznamkovy blok")
        self.assertEqual(result, "Poznámkový blok bol otvorený.")
        mock_press.assert_any_call('win')
        mock_write.assert_called_once_with('notepad')
        mock_press.assert_any_call('enter')

    @patch('gemini_bot.pyautogui.write')
    def test_automate_pc_task_write_notepad(self, mock_write):
        import gemini_bot
        result = gemini_bot.automate_pc_task("napis do poznamkoveho bloku Ahoj svet")
        self.assertEqual(result, "Text 'Ahoj svet' bol napísaný do Poznámkového bloku.")
        mock_write.assert_called_once_with('Ahoj svet', interval=0.05)

    @patch('gemini_bot.requests.post')
    @patch('builtins.open', new_callable=MagicMock)
    @patch('os.path.exists', return_value=False) # Aby sa neprepisoval existujúci súbor
    def test_generate_and_save_code_success(self, mock_exists, mock_open, mock_post):
        import gemini_bot
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "candidates": [
                {
                    "content": {
                        "parts": [
                            {"text": "print('Hello, World!')"}
                        ]
                    }
                }
            ]
        }
        mock_post.return_value = mock_response

        task = "jednoduchy program 'Hello, World!'"
        result = gemini_bot.generate_and_save_code(task)
        
        self.assertIn("Kód pre 'jednoduchy program 'Hello, World!'' bol vygenerovaný a uložený do 'generated_code.py'.", result)
        mock_post.assert_called_once()
        
        # Upravené overenie volania write
        mock_open.return_value.__enter__.return_value.write.assert_called_once_with("print('Hello, World!')")


    @patch('gemini_bot.requests.post')
    def test_generate_and_save_code_failure(self, mock_post):
        import gemini_bot
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "API Error"
        mock_post.return_value = mock_response

        task = "nejaky program"
        result = gemini_bot.generate_and_save_code(task)
        self.assertIn("Chyba API pri generovaní kódu: 500", result)
        self.assertIn("Odpoveď: API Error", result)
        mock_post.assert_called_once()


if __name__ == '__main__':
    unittest.main(exit=False) # exit=False, aby sa neukončil program po testoch
